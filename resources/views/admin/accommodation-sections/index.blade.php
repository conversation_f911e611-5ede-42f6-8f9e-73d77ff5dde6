@extends('admin.layouts.app')

@section('title', 'Accommodation Sections')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <div>
            <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Accommodation Sections</h1>
            <p class="text-gray-600 mt-1 text-sm sm:text-base">Manage luxury accommodations content</p>
        </div>
        <a href="{{ route('admin.accommodation-sections.create') }}" class="inline-flex items-center justify-center px-4 py-2 bg-golden-600 text-white rounded-lg hover:bg-golden-700 transition-colors duration-200 text-sm sm:text-base">
            <i class="fas fa-plus mr-2"></i>+ Add New
        </a>
    </div>

    <!-- Accommodation Sections Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-bed mr-2 text-golden-600"></i>Accommodation Sections
            </h3>
        </div>
        
        @if($accommodationSections->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Images</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($accommodationSections as $section)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ $section->sort_order }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $section->title }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate">{{ $section->description }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex space-x-1">
                                        @if($section->main_image)
                                            <img src="{{ $section->main_image_url }}" alt="Main" class="w-8 h-8 rounded object-cover">
                                        @endif
                                        @if($section->image_1)
                                            <img src="{{ $section->image_1_url }}" alt="Image 1" class="w-8 h-8 rounded object-cover">
                                        @endif
                                        @if($section->image_2)
                                            <img src="{{ $section->image_2_url }}" alt="Image 2" class="w-8 h-8 rounded object-cover">
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($section->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>Inactive
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <a href="{{ route('admin.accommodation-sections.edit', $section) }}" class="text-golden-600 hover:text-golden-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.accommodation-sections.destroy', $section) }}" method="POST" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this accommodation section?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-bed text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No accommodation sections found</h3>
                <p class="text-gray-500 mb-6">Get started by creating your first accommodation section.</p>
                <a href="{{ route('admin.accommodation-sections.create') }}" class="inline-flex items-center px-4 py-2 bg-golden-600 text-white rounded-lg hover:bg-golden-700 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>+ Add New
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
